
import React from 'react';
import { PropertyType, DirectnessType } from '../types';
import { PROPERTY_TYPE_OPTIONS, DIRECTNESS_TYPE_OPTIONS } from '../constants'; // Corrected imports

export interface Filters {
  keyword?: string;
  propertyType?: PropertyType | '';
  location?: string;
  minArea?: number | '';
  maxArea?: number | '';
  minPrice?: number | '';
  maxPrice?: number | '';
  directnessType?: DirectnessType | '';
  sender?: string;
  brokers?: number | '';
}

interface FilterPanelProps {
  filters: Filters;
  onFilterChange: <K extends keyof Filters>(key: K, value: Filters[K]) => void;
  onResetFilters: () => void;
  isOfferPanel: boolean; // To show offer-specific filters like directness
}

const FilterPanel: React.FC<FilterPanelProps> = ({ filters, onFilterChange, onResetFilters, isOfferPanel }) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | number | undefined = value;
    if (type === 'number') {
      processedValue = value === '' ? '' : parseFloat(value);
    }
    if (name === 'propertyType' && value === '') {
        onFilterChange(name as keyof Filters, '' as any);
    } else if (name === 'directnessType' && value === '') {
        onFilterChange(name as keyof Filters, '' as any);
    }
    else {
        onFilterChange(name as keyof Filters, processedValue as any);
    }
  };

  return (
    <div className="p-4 bg-white shadow rounded-lg mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Filter {isOfferPanel ? 'Offers' : 'Requests'}</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div>
          <label htmlFor="keyword" className="block text-sm font-medium text-gray-700">Keyword</label>
          <input
            type="text"
            name="keyword"
            id="keyword"
            value={filters.keyword || ''}
            onChange={handleInputChange}
            placeholder="e.g., Duplex, Sea view"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700">Property Type</label>
          <select
            name="propertyType"
            id="propertyType"
            value={filters.propertyType || ''}
            onChange={handleInputChange}
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">All Types</option>
            {PROPERTY_TYPE_OPTIONS.map(type => <option key={type} value={type}>{type}</option>)}
          </select>
        </div>
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700">Location</label>
          <input
            type="text"
            name="location"
            id="location"
            value={filters.location || ''}
            onChange={handleInputChange}
            placeholder="e.g., Riyadh, Al Malqa"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="sender" className="block text-sm font-medium text-gray-700">Sender (Name/ID)</label>
          <input
            type="text"
            name="sender"
            id="sender"
            value={filters.sender || ''}
            onChange={handleInputChange}
            placeholder="e.g., John Doe or +123456789"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="minArea" className="block text-sm font-medium text-gray-700">Min Area (sqm)</label>
          <input
            type="number"
            name="minArea"
            id="minArea"
            value={filters.minArea === undefined ? '' : filters.minArea}
            onChange={handleInputChange}
            placeholder="e.g., 100"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="maxArea" className="block text-sm font-medium text-gray-700">Max Area (sqm)</label>
          <input
            type="number"
            name="maxArea"
            id="maxArea"
            value={filters.maxArea === undefined ? '' : filters.maxArea}
            onChange={handleInputChange}
            placeholder="e.g., 500"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="minPrice" className="block text-sm font-medium text-gray-700">Min Price/Budget</label>
          <input
            type="number"
            name="minPrice"
            id="minPrice"
            value={filters.minPrice === undefined ? '' : filters.minPrice}
            onChange={handleInputChange}
            placeholder="e.g., 500000"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        <div>
          <label htmlFor="maxPrice" className="block text-sm font-medium text-gray-700">Max Price/Budget</label>
          <input
            type="number"
            name="maxPrice"
            id="maxPrice"
            value={filters.maxPrice === undefined ? '' : filters.maxPrice}
            onChange={handleInputChange}
            placeholder="e.g., 2000000"
            className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
        </div>
        {isOfferPanel && (
          <>
            <div>
              <label htmlFor="directnessType" className="block text-sm font-medium text-gray-700">Directness Level</label>
              <select
                name="directnessType"
                id="directnessType"
                value={filters.directnessType || ''}
                onChange={handleInputChange}
                className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">All Levels</option>
                {DIRECTNESS_TYPE_OPTIONS.map(type => <option key={type} value={type}>{type}</option>)}
              </select>
            </div>
            {filters.directnessType === DirectnessType.INDIRECT && (
               <div>
                <label htmlFor="brokers" className="block text-sm font-medium text-gray-700">Number of Brokers</label>
                <input
                  type="number"
                  name="brokers"
                  id="brokers"
                  value={filters.brokers === undefined ? '' : filters.brokers}
                  onChange={handleInputChange}
                  placeholder="e.g., 2"
                  min="1"
                  className="mt-1 block w-full p-2 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            )}
          </>
        )}
      </div>
      <div className="mt-6 flex justify-end">
        <button
          onClick={onResetFilters}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Reset Filters
        </button>
      </div>
    </div>
  );
};

export default FilterPanel;
