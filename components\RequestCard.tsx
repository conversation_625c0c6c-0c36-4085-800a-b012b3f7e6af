
import React from 'react';
import { PropertyRequest } from '../types';
import { MapPinIcon, BuildingOfficeIcon, TagIcon, UsersIcon, TrashIcon, PencilSquareIcon, UserCircleIcon } from './Icons';

interface RequestCardProps {
  request: PropertyRequest;
  onEdit: (request: PropertyRequest) => void;
  onDelete: (requestId: string) => void;
}

const RequestCard: React.FC<RequestCardProps> = ({ request, onEdit, onDelete }) => {
  const formatRange = (min?: number, max?: number, unit: string = '') => {
    if (min && max && min !== 0 && max !== Infinity) return `${min}${unit} - ${max}${unit}`;
    if (min && min !== 0) return `Min ${min}${unit}`;
    if (max && max !== Infinity) return `Max ${max}${unit}`;
    return 'Any';
  };

  const formatPriceRange = (min?: number, max?: number, currency?: string) => {
    const c = currency ? ` ${currency}` : '';
    if (min && max && min !== 0 && max !== Infinity) return `${new Intl.NumberFormat('en-US').format(min)}${c} - ${new Intl.NumberFormat('en-US').format(max)}${c}`;
    if (min && min !== 0) return `Min ${new Intl.NumberFormat('en-US').format(min)}${c}`;
    if (max && max !== Infinity) return `Max ${new Intl.NumberFormat('en-US').format(max)}${c}`;
    return 'Any Budget';
  };

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden transition-shadow duration-300 hover:shadow-xl">
      <div className="p-5">
        <div className="flex justify-between items-start">
           <h3 className="text-xl font-semibold text-secondary-700 mb-1">
            Request: {request.propertyType.join(', ') || 'Any Type'}
          </h3>
        </div>
         <p className="text-sm text-gray-500 mb-3 flex items-center">
            <MapPinIcon className="w-4 h-4 mr-2 text-gray-400" />
            Locations: {request.location.join(', ') || 'Any Location'}
        </p>

        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-700 mb-3">
          <div className="flex items-center">
            <BuildingOfficeIcon className="w-4 h-4 mr-2 text-primary-500" />
            Area: {formatRange(request.areaRange?.min, request.areaRange?.max, ' m²')}
          </div>
          <div className="flex items-center">
            <TagIcon className="w-4 h-4 mr-2 text-primary-500" />
            Budget: {formatPriceRange(request.budgetRange?.min, request.budgetRange?.max, request.currency)}
          </div>
          {request.rooms && (
            <div className="flex items-center">
               <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-2 text-primary-500"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>
              Rooms: {request.rooms}
            </div>
          )}
          {request.bathrooms && (
             <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-2 text-primary-500"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>
              Bathrooms: {request.bathrooms}
            </div>
          )}
          {request.finishType && <p>Finish: {request.finishType}</p>}
          {typeof request.isFurnished === 'boolean' && <p>Furnished: {request.isFurnished ? 'Yes' : 'No'}</p>}
        </div>

        {request.features && request.features.length > 0 && (
          <div className="mb-3">
            <h4 className="text-xs font-semibold text-gray-500 mb-1">Desired Features:</h4>
            <div className="flex flex-wrap gap-2">
              {request.features.map((feature, index) => (
                <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">{feature}</span>
              ))}
            </div>
          </div>
        )}
        
        {request.description && <p className="text-xs text-gray-600 mb-3 bg-gray-50 p-2 rounded-md">{request.description}</p>}
        {request.originalMessage && (
            <details className="mb-3">
                <summary className="text-xs text-gray-500 cursor-pointer hover:text-secondary-600">View Original Message</summary>
                <p className="text-xs text-gray-600 mt-1 p-2 border rounded-md bg-gray-50 max-h-20 overflow-y-auto">{request.originalMessage}</p>
            </details>
        )}
      </div>
      <div className="px-5 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-xs text-gray-600 flex items-center">
              <UserCircleIcon className="w-4 h-4 mr-1 text-gray-400" />
              Requester: {request.sender.name || 'N/A'} ({request.sender.id})
            </p>
            {request.sender.groupName && (
              <p className="text-xs text-gray-500 flex items-center mt-0.5">
                <UsersIcon className="w-4 h-4 mr-1 text-gray-400" />
                Group: {request.sender.groupName}
              </p>
            )}
            <p className="text-xs text-gray-400 mt-1">
              Requested: {new Date(request.createdAt).toLocaleDateString()}
              {request.updatedAt && ` (Updated: ${new Date(request.updatedAt).toLocaleDateString()})`}
            </p>
          </div>
          <div className="flex space-x-2">
            <button onClick={() => onEdit(request)} className="text-secondary-600 hover:text-secondary-800" title="Edit Request">
              <PencilSquareIcon className="w-5 h-5" />
            </button>
            <button onClick={() => onDelete(request.id)} className="text-red-500 hover:text-red-700" title="Delete Request">
              <TrashIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestCard;
