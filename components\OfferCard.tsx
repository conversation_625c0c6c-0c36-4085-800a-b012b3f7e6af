
import React from 'react';
import { PropertyOffer, DirectnessType } from '../types';
import { MapPinIcon, BuildingOfficeIcon, TagIcon, UsersIcon, TrashIcon, PencilSquareIcon, UserCircleIcon } from './Icons'; 
import { DIRECTNESS_COLORS } from '../constants';


interface OfferCardProps {
  offer: PropertyOffer;
  onEdit: (offer: PropertyOffer) => void;
  onDelete: (offerId: string) => void;
}

const OfferCard: React.FC<OfferCardProps> = ({ offer, onEdit, onDelete }) => {
  const directnessColorClass = DIRECTNESS_COLORS[offer.directness.type] || "bg-gray-100 text-gray-700";
  
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: currency, minimumFractionDigits: 0 }).format(price);
  };

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden transition-shadow duration-300 hover:shadow-xl">
      <div className="p-5">
        <div className="flex justify-between items-start">
          <h3 className="text-xl font-semibold text-primary-700 mb-1">{offer.propertyType} - {offer.location.split(',')[0]}</h3>
          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${directnessColorClass}`}>
            {offer.directness.type}
            {offer.directness.type === DirectnessType.INDIRECT && offer.directness.brokers ? ` (${offer.directness.brokers} broker${offer.directness.brokers > 1 ? 's' : ''})` : ''}
          </span>
        </div>
        <p className="text-sm text-gray-500 mb-3 flex items-center">
          <MapPinIcon className="w-4 h-4 mr-2 text-gray-400" />
          {offer.location}
        </p>

        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-700 mb-3">
          <div className="flex items-center">
            <BuildingOfficeIcon className="w-4 h-4 mr-2 text-secondary-500" />
            Area: {offer.area} m²
          </div>
          <div className="flex items-center">
            <TagIcon className="w-4 h-4 mr-2 text-secondary-500" />
            Price: {formatPrice(offer.price, offer.currency)}
          </div>
          {offer.rooms && (
            <div className="flex items-center">
               <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-2 text-secondary-500"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>
              Rooms: {offer.rooms}
            </div>
          )}
          {offer.bathrooms && (
             <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-2 text-secondary-500"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>
              Bathrooms: {offer.bathrooms}
            </div>
          )}
          {offer.finishType && <p>Finish: {offer.finishType}</p>}
          {offer.isFurnished && <p>Furnished: Yes</p>}
        </div>

        {offer.features && offer.features.length > 0 && (
          <div className="mb-3">
            <h4 className="text-xs font-semibold text-gray-500 mb-1">Features:</h4>
            <div className="flex flex-wrap gap-2">
              {offer.features.map((feature, index) => (
                <span key={index} className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">{feature}</span>
              ))}
            </div>
          </div>
        )}
        
        {offer.description && <p className="text-xs text-gray-600 mb-3 bg-gray-50 p-2 rounded-md">{offer.description}</p>}
        
        {offer.originalMessage && (
            <details className="mb-3">
                <summary className="text-xs text-gray-500 cursor-pointer hover:text-primary-600">View Original Message</summary>
                <p className="text-xs text-gray-600 mt-1 p-2 border rounded-md bg-gray-50 max-h-20 overflow-y-auto">{offer.originalMessage}</p>
            </details>
        )}

      </div>
      <div className="px-5 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-xs text-gray-600 flex items-center">
              <UserCircleIcon className="w-4 h-4 mr-1 text-gray-400" />
              Sender: {offer.sender.name || 'N/A'} ({offer.sender.id})
            </p>
            {offer.sender.groupName && (
              <p className="text-xs text-gray-500 flex items-center mt-0.5">
                <UsersIcon className="w-4 h-4 mr-1 text-gray-400" />
                Group: {offer.sender.groupName}
              </p>
            )}
            <p className="text-xs text-gray-400 mt-1">
              Listed: {new Date(offer.createdAt).toLocaleDateString()}
              {offer.updatedAt && ` (Updated: ${new Date(offer.updatedAt).toLocaleDateString()})`}
            </p>
          </div>
          <div className="flex space-x-2">
            <button onClick={() => onEdit(offer)} className="text-primary-600 hover:text-primary-800" title="Edit Offer">
              <PencilSquareIcon className="w-5 h-5" />
            </button>
            <button onClick={() => onDelete(offer.id)} className="text-red-500 hover:text-red-700" title="Delete Offer">
              <TrashIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfferCard;
