
import React, { useState, useEffect } from 'react';
import { PropertyOffer, PropertyType, DirectnessType, DirectnessInfo } from '../types';
import { DEFAULT_CURRENCY } from '../constants'; // Corrected import
import PropertyFormFields from './PropertyFormFields';

interface OfferFormProps {
  onSubmit: (offer: PropertyOffer) => void;
  onCancel: () => void;
  initialData?: Partial<PropertyOffer>;
}

const OfferForm: React.FC<OfferFormProps> = ({ onSubmit, onCancel, initialData }) => {
  const [formData, setFormData] = useState<Partial<PropertyOffer>>(
    initialData || {
      currency: DEFAULT_CURRENCY,
      directness: { type: DirectnessType.DIRECT },
      sender: {id: ''}
    }
  );

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | number | boolean | string[] = value;

    if (type === 'number') {
      processedValue = value === '' ? '' : parseFloat(value);
    } else if ((e.target as HTMLInputElement).type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    if (name.includes('.')) {
        const [parentKey, childKey] = name.split('.');
        setFormData(prev => ({
            ...prev,
            [parentKey]: {
                ...(prev as any)[parentKey],
                [childKey]: processedValue
            }
        }));
    } else {
        setFormData(prev => ({ ...prev, [name]: processedValue }));
    }
  };
  
  const handleDirectnessChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    const fieldName = name.split('.')[1]; // 'type' or 'brokers'
    
    setFormData(prev => {
        const newDirectness: DirectnessInfo = { ...(prev.directness || { type: DirectnessType.DIRECT }) };
        if (fieldName === 'type') {
            newDirectness.type = value as DirectnessType;
            if (value !== DirectnessType.INDIRECT) {
                delete newDirectness.brokers; // Remove brokers if not indirect
            }
        } else if (fieldName === 'brokers') {
            newDirectness.brokers = value === '' ? undefined : parseInt(value, 10);
        }
        return { ...prev, directness: newDirectness };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Basic validation
    if (!formData.propertyType || !formData.location || !formData.area || !formData.price || !formData.directness?.type || !formData.sender?.id) {
      alert('Please fill all required fields for the offer.');
      return;
    }
    const fullOfferData: PropertyOffer = {
      id: initialData?.id || new Date().toISOString(), // Use existing ID or generate new
      createdAt: initialData?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...formData,
      propertyType: formData.propertyType as PropertyType,
      location: formData.location as string,
      area: Number(formData.area),
      price: Number(formData.price),
      currency: formData.currency || DEFAULT_CURRENCY,
      directness: formData.directness as DirectnessInfo,
      sender: formData.sender as {id: string, name?: string, groupName?: string},
    };
    onSubmit(fullOfferData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PropertyFormFields<PropertyOffer>
        formData={formData}
        handleChange={handleChange}
        handleDirectnessChange={handleDirectnessChange}
        setFormData={setFormData}
        isOffer={true}
      />
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {initialData ? 'Update Offer' : 'Add Offer'}
        </button>
      </div>
    </form>
  );
};

export default OfferForm;
