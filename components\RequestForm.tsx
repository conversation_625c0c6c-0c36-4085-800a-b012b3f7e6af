
import React, { useState, useEffect } from 'react';
import { PropertyRequest, PropertyType, DirectnessInfo, DirectnessType } from '../types'; 
import { DEFAULT_CURRENCY } from '../constants'; // Corrected import
import PropertyFormFields from './PropertyFormFields';

interface RequestFormProps {
  onSubmit: (request: PropertyRequest) => void;
  onCancel: () => void;
  initialData?: Partial<PropertyRequest>;
}

const RequestForm: React.FC<RequestFormProps> = ({ onSubmit, onCancel, initialData }) => {
  const [formData, setFormData] = useState<Partial<PropertyRequest>>(
    initialData || {
      propertyType: [],
      location: [],
      currency: DEFAULT_CURRENCY, // This is now valid as PropertyRequest has currency
      sender: {id: ''}
    }
  );

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | number | boolean | string[] = value;
    
    if (type === 'number') {
      processedValue = value === '' ? '' : parseFloat(value);
    } else if ((e.target as HTMLInputElement).type === 'checkbox' && name !== 'propertyType') { // propertyType checkbox handled by PropertyFormFields
      processedValue = (e.target as HTMLInputElement).checked;
    }

    if (name.includes('.')) {
        const [parentKey, childKey] = name.split('.');
        setFormData(prev => ({
            ...prev,
            [parentKey]: {
                ...(prev as any)[parentKey],
                [childKey]: processedValue
            }
        }));
    } else {
        setFormData(prev => ({ ...prev, [name]: processedValue }));
    }
  };

  // Dummy handler as directness is not part of Request, but PropertyFormFields expects it.
  const handleDirectnessChange = () => {};


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.propertyType || formData.propertyType.length === 0 || !formData.location || formData.location.length === 0 || !formData.sender?.id) {
      alert('Please fill all required fields for the request (Property Type, Location, Sender ID).');
      return;
    }
    
    const fullRequestData: PropertyRequest = {
      id: initialData?.id || new Date().toISOString(),
      createdAt: initialData?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...formData,
      propertyType: formData.propertyType as PropertyType[],
      location: formData.location as string[],
      currency: formData.currency || DEFAULT_CURRENCY, // This is now valid
      sender: formData.sender as {id: string, name?: string, groupName?: string},
    };
    onSubmit(fullRequestData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PropertyFormFields<PropertyRequest>
        formData={formData}
        handleChange={handleChange}
        handleDirectnessChange={handleDirectnessChange} // Pass dummy for type compliance
        setFormData={setFormData}
        isOffer={false}
      />
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {initialData ? 'Update Request' : 'Add Request'}
        </button>
      </div>
    </form>
  );
};

export default RequestForm;
