
import React from 'react';
import { PropertyType, DirectnessType, DirectnessInfo, PropertyOffer } from '../types'; 
import { CURRENCY_OPTIONS, PROPERTY_TYPE_OPTIONS, DIRECTNESS_TYPE_OPTIONS, DEFAULT_CURRENCY, MIN_BROKERS, MAX_BROKERS } from '../constants'; // Corrected imports
import { LightBulbIcon } from './Icons'; 
import { extractPropertyDetailsFromText } from '../services/geminiService';

interface CommonProps<T> {
  formData: Partial<T>;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleDirectnessChange: (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => void;
  setFormData: React.Dispatch<React.SetStateAction<Partial<T>>>;
  isOffer: boolean;
}

const PropertyFormFields = <T extends {directness?: DirectnessInfo, propertyType?: PropertyType | PropertyType[], sender?: { name?: string, id?: string}, location?: string | string[], areaRange?: {min: number, max: number}, budgetRange?: {min: number, max: number}, currency?: string }> (
    { formData, handleChange, handleDirectnessChange, setFormData, isOffer }: CommonProps<T>
  ) => {
  const [rawMessage, setRawMessage] = React.useState('');
  const [isExtracting, setIsExtracting] = React.useState(false);
  const [extractionError, setExtractionError] = React.useState<string | null>(null);

  const handleExtractWithAI = async () => {
    if (!rawMessage.trim()) {
      setExtractionError("Please paste a message to extract from.");
      return;
    }
    setIsExtracting(true);
    setExtractionError(null);
    try {
      const extractedDetails = await extractPropertyDetailsFromText(rawMessage, isOffer ? 'offer' : 'request');
      if (extractedDetails) {
        // Special handling for directness as it's an object
        let directnessToSet: DirectnessInfo | undefined = undefined;
        if (isOffer && (extractedDetails as Partial<PropertyOffer>).directness?.type) { // AI might return directness object directly
             directnessToSet = (extractedDetails as Partial<PropertyOffer>).directness;
        } else if (isOffer && (extractedDetails as any).directnessType) { // AI might return directnessType as a string
            directnessToSet = {
                type: (extractedDetails as any).directnessType as DirectnessType,
                brokers: (extractedDetails as any).brokers ? Number((extractedDetails as any).brokers) : undefined,
            };
        }
        
        // Handle sender info
        const senderInfoFromAI = {
          name: (extractedDetails as any).senderName, // AI might return senderName
          id: (extractedDetails as any).senderContact,  // AI might return senderContact
        };
        const existingSenderInfo = formData.sender || {};
        const mergedSenderInfo = {
            ...existingSenderInfo,
            ...(senderInfoFromAI.name && {name: senderInfoFromAI.name}),
            ...(senderInfoFromAI.id && {id: senderInfoFromAI.id}),
        };


        // Remove AI specific helper fields from extractedDetails before merging if they exist
        const cleanedExtractedDetails = {...extractedDetails};
        delete (cleanedExtractedDetails as any).directnessType;
        delete (cleanedExtractedDetails as any).brokers; 
        // The problematic line was here and has been removed as it's syntactically incorrect and logically redundant.
        // `directnessToSet` will correctly overwrite the directness property in `setFormData`.
        delete (cleanedExtractedDetails as any).senderName;
        delete (cleanedExtractedDetails as any).senderContact;


        setFormData(prev => ({
          ...prev,
          ...cleanedExtractedDetails,
          ...(directnessToSet && { directness: directnessToSet }),
          ...(mergedSenderInfo.id || mergedSenderInfo.name ? {sender: mergedSenderInfo} : {}),
        }));
        setRawMessage(''); // Clear input after successful extraction
      } else {
        setExtractionError("AI could not extract details. Please fill manually.");
      }
    } catch (error) {
      console.error("Extraction error:", error);
      setExtractionError("An error occurred during AI extraction. Please try again or fill manually.");
    }
    setIsExtracting(false);
  };

  const currentCurrency = formData.currency || DEFAULT_CURRENCY;

  return (
    <div className="space-y-6">
      <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
        <h4 className="text-md font-semibold text-blue-700 mb-2 flex items-center">
          <LightBulbIcon className="w-5 h-5 mr-2" />
          AI-Powered Extraction
        </h4>
        <p className="text-sm text-blue-600 mb-2">Paste the WhatsApp message below and click "Extract with AI" to auto-fill the form.</p>
        <textarea
          name="rawMessage"
          value={rawMessage}
          onChange={(e) => setRawMessage(e.target.value)}
          placeholder="Paste WhatsApp message here..."
          rows={4}
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
        />
        <button
          type="button"
          onClick={handleExtractWithAI}
          disabled={isExtracting || !rawMessage.trim()}
          className="mt-2 w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 transition-colors"
        >
          {isExtracting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Extracting...
            </>
          ) : "Extract with AI"}
        </button>
        {extractionError && <p className="text-red-500 text-sm mt-2">{extractionError}</p>}
      </div>

      <div>
        <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700">Property Type {isOffer ? '' : '(can select multiple)'}</label>
        {isOffer ? (
          <select
            id="propertyType"
            name="propertyType"
            value={(formData as any).propertyType || ''}
            onChange={handleChange}
            required
            className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">Select type</option>
            {PROPERTY_TYPE_OPTIONS.map(type => <option key={type} value={type}>{type}</option>)}
          </select>
        ) : (
          <div className="mt-1 grid grid-cols-2 sm:grid-cols-3 gap-2">
            {PROPERTY_TYPE_OPTIONS.map(type => (
              <label key={type} className="flex items-center space-x-2 p-2 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
                <input
                  type="checkbox"
                  name="propertyType"
                  value={type}
                  checked={Array.isArray((formData as any).propertyType) && (formData as any).propertyType.includes(type)}
                  onChange={(e) => {
                    const currentTypes = Array.isArray((formData as any).propertyType) ? (formData as any).propertyType : [];
                    const newTypes = e.target.checked ? [...currentTypes, type] : currentTypes.filter(t => t !== type);
                    setFormData(prev => ({ ...prev, propertyType: newTypes as any }));
                  }}
                  className="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">{type}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      <div>
        <label htmlFor="location" className="block text-sm font-medium text-gray-700">Location {isOffer ? '(City, Neighborhood, Street)' : '(Preferred Locations, comma-separated)'}</label>
        <input
          type="text"
          name="location"
          id="location"
          value={isOffer ? (formData as any).location || '' : ((formData as any).location || []).join(', ')}
          onChange={isOffer ? handleChange : (e) => setFormData(prev => ({ ...prev, location: e.target.value.split(',').map(s => s.trim()) as any }))}
          required
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {isOffer ? (
          <div>
            <label htmlFor="area" className="block text-sm font-medium text-gray-700">Area (sqm)</label>
            <input
              type="number"
              name="area"
              id="area"
              value={(formData as any).area || ''}
              onChange={handleChange}
              required
              className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="areaRangeMin" className="block text-sm font-medium text-gray-700">Min Area (sqm)</label>
              <input type="number" name="areaRange.min" id="areaRangeMin" value={(formData.areaRange as any)?.min || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="areaRangeMax" className="block text-sm font-medium text-gray-700">Max Area (sqm)</label>
              <input type="number" name="areaRange.max" id="areaRangeMax" value={(formData.areaRange as any)?.max || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
            </div>
          </div>
        )}
        
        {isOffer ? (
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700">Price</label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <input
                type="number"
                name="price"
                id="price"
                value={(formData as any).price || ''}
                onChange={handleChange}
                required
                className="flex-1 block w-full min-w-0 p-2 rounded-none rounded-l-md border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
              <select
                name="currency"
                value={currentCurrency}
                onChange={handleChange}
                className="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm"
              >
                {CURRENCY_OPTIONS.map(c => <option key={c} value={c}>{c}</option>)}
              </select>
            </div>
          </div>
        ) : (
           <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="budgetRangeMin" className="block text-sm font-medium text-gray-700">Min Budget</label>
               <div className="mt-1 flex rounded-md shadow-sm">
                <input type="number" name="budgetRange.min" id="budgetRangeMin" value={(formData.budgetRange as any)?.min || ''} onChange={handleChange} className="flex-1 block w-full min-w-0 p-2 rounded-none rounded-l-md border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
                 <select name="currency" value={currentCurrency} onChange={handleChange} className="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    {CURRENCY_OPTIONS.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>
            </div>
            <div>
              <label htmlFor="budgetRangeMax" className="block text-sm font-medium text-gray-700">Max Budget</label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <input type="number" name="budgetRange.max" id="budgetRangeMax" value={(formData.budgetRange as any)?.max || ''} onChange={handleChange} className="flex-1 block w-full min-w-0 p-2 rounded-none rounded-l-md border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
                 <select name="currency" value={currentCurrency} onChange={handleChange} className="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    {CURRENCY_OPTIONS.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="rooms" className="block text-sm font-medium text-gray-700">Rooms (Optional)</label>
          <input type="number" name="rooms" id="rooms" value={(formData as any).rooms || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
        </div>
        <div>
          <label htmlFor="bathrooms" className="block text-sm font-medium text-gray-700">Bathrooms (Optional)</label>
          <input type="number" name="bathrooms" id="bathrooms" value={(formData as any).bathrooms || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="finishType" className="block text-sm font-medium text-gray-700">Finish Type (Optional)</label>
          <input type="text" name="finishType" id="finishType" value={(formData as any).finishType || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
        </div>
        <div className="flex items-center pt-6">
          <input
            id="isFurnished"
            name="isFurnished"
            type="checkbox"
            checked={(formData as any).isFurnished || false}
            onChange={handleChange}
            className="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
          <label htmlFor="isFurnished" className="ml-2 block text-sm text-gray-900">Is Furnished?</label>
        </div>
      </div>

      {isOffer && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="directness.type" className="block text-sm font-medium text-gray-700">Directness Level</label>
            <select
              id="directness.type"
              name="directness.type"
              value={formData.directness?.type || ''}
              onChange={handleDirectnessChange}
              required
              className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">Select directness</option>
              {DIRECTNESS_TYPE_OPTIONS.map(type => <option key={type} value={type}>{type}</option>)}
            </select>
          </div>
          {formData.directness?.type === DirectnessType.INDIRECT && (
            <div>
              <label htmlFor="directness.brokers" className="block text-sm font-medium text-gray-700">Number of Brokers</label>
              <input
                type="number"
                name="directness.brokers"
                id="directness.brokers"
                value={formData.directness?.brokers || ''}
                onChange={handleDirectnessChange}
                min={MIN_BROKERS}
                max={MAX_BROKERS}
                className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          )}
        </div>
      )}
      
      <div>
        <label htmlFor="features" className="block text-sm font-medium text-gray-700">Features (Optional, comma-separated)</label>
        <input
          type="text"
          name="features"
          id="features"
          value={((formData as any).features || []).join(', ')}
          onChange={(e) => setFormData(prev => ({...prev, features: e.target.value.split(',').map(s => s.trim()) as any}))}
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description / Notes (Optional)</label>
        <textarea
          name="description"
          id="description"
          rows={3}
          value={(formData as any).description || ''}
          onChange={handleChange}
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        />
      </div>
       <div>
        <label htmlFor="originalMessage" className="block text-sm font-medium text-gray-700">Original Message (Optional, for reference)</label>
        <textarea
          name="originalMessage"
          id="originalMessage"
          rows={3}
          value={(formData as any).originalMessage || rawMessage} // Show rawMessage if formData.originalMessage is empty, allowing AI to prefill and user to see it
          onChange={handleChange}
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-gray-50"
          placeholder="Original WhatsApp message can be stored here."
        />
      </div>
       <div className="border-t pt-4 mt-4">
         <h4 className="text-md font-semibold text-gray-700 mb-2">Sender Information</h4>
         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label htmlFor="sender.name" className="block text-sm font-medium text-gray-700">Sender Name</label>
                <input type="text" name="sender.name" id="sender.name" value={formData.sender?.name || ''} onChange={handleChange} className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
            </div>
            <div>
                <label htmlFor="sender.id" className="block text-sm font-medium text-gray-700">Sender Contact/ID (e.g. WhatsApp number)</label>
                <input type="text" name="sender.id" id="sender.id" value={formData.sender?.id || ''} onChange={handleChange} required className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm" />
            </div>
         </div>
       </div>
    </div>
  );
};

export default PropertyFormFields;
