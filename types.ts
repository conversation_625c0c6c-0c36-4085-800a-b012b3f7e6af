
export enum PropertyType {
  APARTMENT = "Apartment",
  VILLA = "Villa",
  LAND = "Land",
  COMMERCIAL = "Commercial",
  OTHER = "Other",
}

export enum DirectnessType {
  VERY_DIRECT = "Very Direct (Owner)",
  DIRECT = "Direct (Owner/Agent)",
  INDIRECT = "Indirect",
}

export interface DirectnessInfo {
  type: DirectnessType;
  brokers?: number; // Number of intermediaries if indirect
}

export interface SenderInfo {
  id: string; // WhatsApp number or unique ID
  name?: string; // Contact name
  groupName?: string; // Group where message originated
}

export interface PropertyOffer {
  id: string;
  propertyType: PropertyType;
  location: string; // City, neighborhood, street
  area: number; // in sqm
  price: number;
  currency: string;
  rooms?: number;
  bathrooms?: number;
  finishType?: string;
  isFurnished?: boolean;
  features?: string[];
  description?: string;
  images?: string[]; // URLs or base64
  directness: DirectnessInfo;
  sender: SenderInfo;
  originalMessage?: string;
  createdAt: string; // ISO Date string
  updatedAt?: string; // ISO Date string
}

export interface PropertyRequest {
  id: string;
  propertyType: PropertyType[]; // Can be multiple types
  location: string[]; // Preferred locations
  areaRange?: { min: number; max: number };
  budgetRange?: { min: number; max: number };
  currency?: string; // Added currency field
  rooms?: number;
  bathrooms?: number;
  finishType?: string;
  isFurnished?: boolean;
  features?: string[];
  description?: string;
  sender: SenderInfo;
  originalMessage?: string;
  createdAt: string; // ISO Date string
  updatedAt?: string; // ISO Date string
}

export interface Match {
  id: string;
  offer: PropertyOffer;
  request: PropertyRequest;
  matchScore: number; // 0-1
  createdAt: string;
}

export interface AnalyticsData {
  totalOffers: number;
  totalRequests: number;
  offersByType: { type: PropertyType; count: number }[];
  requestsByType: { type: PropertyType; count: number }[];
  popularLocationsOffers: { location: string; count: number }[];
  popularLocationsRequests: { location: string; count: number }[];
  averagePriceByArea: { area: number; price: number }[];
  matchesFound: number;
}

export enum AppView {
  DASHBOARD = "dashboard",
  OFFERS = "offers",
  REQUESTS = "requests",
  MATCHES = "matches",
  ANALYTICS = "analytics",
  SETTINGS = "settings",
  ADD_OFFER = "add_offer",
  ADD_REQUEST = "add_request",
}