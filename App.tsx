
import React, { useState, use<PERSON><PERSON>back, useEffect, useMemo } from 'react';
import { Routes, Route, Link, useLocation, Navigate } from 'react-router-dom';
import { PropertyOffer, PropertyRequest, Match, AppView, PropertyType, DirectnessType, AnalyticsData } from './types';
import { APP_NAME, NAVIGATION_ITEMS, WHATSAPP_STATUS } from './constants';
import OfferForm from './components/OfferForm';
import RequestForm from './components/RequestForm';
import Modal from './components/Modal';
import OfferCard from './components/OfferCard';
import RequestCard from './components/RequestCard';
import FilterPanel, { Filters } from './components/FilterPanel';
import { PlusCircleIcon, ArrowPathIcon, CheckCircleIcon, XCircleIcon, InformationCircleIcon, QrCodeIcon, UserCircleIcon } from './components/Icons'; // Added UserCircleIcon
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

// Mock Data Generation
const generateMockSender = (index: number) => ({
  id: `+9665${Math.floor(10000000 + Math.random() * 90000000)}`,
  name: `Sender ${String.fromCharCode(65 + (index % 26))}${index}`,
  groupName: `Group ${Math.floor(index / 5) + 1}`,
});

const generateMockOffers = (count: number): PropertyOffer[] => Array.from({ length: count }, (_, i) => ({
  id: `offer-${Date.now()}-${i}`,
  propertyType: [PropertyType.APARTMENT, PropertyType.VILLA, PropertyType.LAND][i % 3],
  location: `Riyadh, Al Malqa, Street ${i+1}`,
  area: 100 + i * 20,
  price: 500000 + i * 100000,
  currency: 'SAR',
  rooms: (i % 3) + 2,
  bathrooms: (i % 2) + 1,
  directness: { type: [DirectnessType.VERY_DIRECT, DirectnessType.DIRECT, DirectnessType.INDIRECT][i % 3], brokers: (i % 3 === 2) ? (i % 2) + 1 : undefined },
  sender: generateMockSender(i),
  description: `This is a mock offer description for property ${i+1}. It has some nice features.`,
  originalMessage: `Original WhatsApp message for offer ${i+1}: Villa for sale, Riyadh, Al Malqa, 250m, 1.2M SAR. Direct from owner. Call ${generateMockSender(i).id}.`,
  createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
}));

const generateMockRequests = (count: number): PropertyRequest[] => Array.from({ length: count }, (_, i) => ({
  id: `request-${Date.now()}-${i}`,
  propertyType: [[PropertyType.APARTMENT], [PropertyType.VILLA, PropertyType.COMMERCIAL]][i % 2],
  location: [`Jeddah, Al Hamra`, `Dammam, Al Faisaliah`][i % 2].split(','),
  areaRange: { min: 80 + i * 10, max: 200 + i * 10 },
  budgetRange: { min: 400000 + i * 50000, max: 1000000 + i * 50000 },
  currency: 'SAR',
  rooms: (i % 2) + 2,
  sender: generateMockSender(i + count), // Ensure unique senders
  description: `Looking for a property matching these criteria for client ${i+1}.`,
  originalMessage: `Original WhatsApp message for request ${i+1}: Required apartment in Jeddah, Al Hamra, 2-3 rooms, budget 600k. Contact ${generateMockSender(i+count).id}.`,
  createdAt: new Date(Date.now() - i * 2 * 24 * 60 * 60 * 1000).toISOString(),
}));


const App: React.FC = () => {
  const [offers, setOffers] = useState<PropertyOffer[]>(() => generateMockOffers(5));
  const [requests, setRequests] = useState<PropertyRequest[]>(() => generateMockRequests(3));
  const [matches, setMatches] = useState<Match[]>([]);
  
  const [isOfferModalOpen, setIsOfferModalOpen] = useState(false);
  const [editingOffer, setEditingOffer] = useState<PropertyOffer | undefined>(undefined);
  
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
  const [editingRequest, setEditingRequest] = useState<PropertyRequest | undefined>(undefined);

  const [offerFilters, setOfferFilters] = useState<Filters>({});
  const [requestFilters, setRequestFilters] = useState<Filters>({});

  const [apiKey, setApiKey] = useState<string>(''); // process.env.API_KEY would be used in a real env
  const [whatsappConnectionStatus, setWhatsappConnectionStatus] = useState<string>(WHATSAPP_STATUS.DISCONNECTED);
  const [showQrPlaceholder, setShowQrPlaceholder] = useState<boolean>(false);
  const [chatFile, setChatFile] = useState<File | null>(null);
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [scanProgress, setScanProgress] = useState<number>(0);


  const location = useLocation();
  const currentView = useMemo(() => {
    const path = location.pathname.substring(1) || AppView.DASHBOARD;
    return path as AppView;
  }, [location.pathname]);


  const handleAddOffer = (offer: PropertyOffer) => {
    setOffers(prev => [offer, ...prev]);
    setIsOfferModalOpen(false);
    setEditingOffer(undefined);
  };

  const handleEditOffer = (offer: PropertyOffer) => {
    setOffers(prev => prev.map(o => o.id === offer.id ? offer : o));
    setIsOfferModalOpen(false);
    setEditingOffer(undefined);
  };

  const handleDeleteOffer = (offerId: string) => {
    if (window.confirm("Are you sure you want to delete this offer?")) {
      setOffers(prev => prev.filter(o => o.id !== offerId));
    }
  };

  const openEditOfferModal = (offer: PropertyOffer) => {
    setEditingOffer(offer);
    setIsOfferModalOpen(true);
  };


  const handleAddRequest = (request: PropertyRequest) => {
    setRequests(prev => [request, ...prev]);
    setIsRequestModalOpen(false);
    setEditingRequest(undefined);
  };

  const handleEditRequest = (request: PropertyRequest) => {
    setRequests(prev => prev.map(r => r.id === request.id ? request : r));
    setIsRequestModalOpen(false);
    setEditingRequest(undefined);
  };

  const handleDeleteRequest = (requestId: string) => {
     if (window.confirm("Are you sure you want to delete this request?")) {
      setRequests(prev => prev.filter(r => r.id !== requestId));
    }
  };

  const openEditRequestModal = (request: PropertyRequest) => {
    setEditingRequest(request);
    setIsRequestModalOpen(true);
  };

  const handleOfferFilterChange = <K extends keyof Filters>(key: K, value: Filters[K]) => {
    setOfferFilters(prev => ({ ...prev, [key]: value }));
  };
  const resetOfferFilters = () => setOfferFilters({});

  const handleRequestFilterChange = <K extends keyof Filters>(key: K, value: Filters[K]) => {
    setRequestFilters(prev => ({ ...prev, [key]: value }));
  };
  const resetRequestFilters = () => setRequestFilters({});

  const applyFilters = <T extends { location: string | string[]; description?: string; sender: { name?: string; id: string }; propertyType: PropertyType | PropertyType[]; area?: number; price?: number; directness?: {type: DirectnessType; brokers?: number}; areaRange?: {min:number; max:number}; budgetRange?: {min:number; max:number}; currency?: string; } >(
    items: T[], 
    filters: Filters,
    isOffer: boolean
  ): T[] => {
    return items.filter(item => {
      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase();
        const itemLocation = Array.isArray(item.location) ? item.location.join(' ') : item.location;
        if (
          !(item.description?.toLowerCase().includes(keyword)) &&
          !(itemLocation.toLowerCase().includes(keyword)) &&
          !(item.sender.name?.toLowerCase().includes(keyword)) &&
          !(item.sender.id.toLowerCase().includes(keyword))
        ) return false;
      }
      if (filters.propertyType && (isOffer ? (item as unknown as PropertyOffer).propertyType !== filters.propertyType : !(item as unknown as PropertyRequest).propertyType.includes(filters.propertyType as PropertyType) ) ) return false;
      if (filters.location) {
        const itemLocation = Array.isArray(item.location) ? item.location.join(' ') : item.location;
        if (!itemLocation.toLowerCase().includes(filters.location.toLowerCase())) return false;
      }
      if (filters.sender) {
         if (
          !(item.sender.name?.toLowerCase().includes(filters.sender.toLowerCase())) &&
          !(item.sender.id.toLowerCase().includes(filters.sender.toLowerCase()))
        ) return false;
      }

      if (isOffer) {
        const offerItem = item as unknown as PropertyOffer;
        if (filters.minArea && offerItem.area < filters.minArea) return false;
        if (filters.maxArea && offerItem.area > filters.maxArea) return false;
        if (filters.minPrice && offerItem.price < filters.minPrice) return false;
        if (filters.maxPrice && offerItem.price > filters.maxPrice) return false;
        if (filters.directnessType && offerItem.directness.type !== filters.directnessType) return false;
        if (filters.directnessType === DirectnessType.INDIRECT && filters.brokers && offerItem.directness.brokers !== filters.brokers) return false;
      } else {
        const requestItem = item as unknown as PropertyRequest;
        if (filters.minArea && requestItem.areaRange?.max && requestItem.areaRange.max < filters.minArea) return false;
        if (filters.maxArea && requestItem.areaRange?.min && requestItem.areaRange.min > filters.maxArea) return false;
        if (filters.minPrice && requestItem.budgetRange?.max && requestItem.budgetRange.max < filters.minPrice) return false;
        if (filters.maxPrice && requestItem.budgetRange?.min && requestItem.budgetRange.min > filters.maxPrice) return false;
      }
      return true;
    });
  };

  const filteredOffers = useMemo(() => applyFilters(offers, offerFilters, true), [offers, offerFilters]);
  const filteredRequests = useMemo(() => applyFilters(requests, requestFilters, false), [requests, requestFilters]);

  const runMatchingEngine = useCallback(() => {
    const newMatches: Match[] = [];
    // Basic matching logic (can be greatly improved)
    offers.forEach(offer => {
      requests.forEach(request => {
        let score = 0;
        if (request.propertyType.includes(offer.propertyType)) score += 0.3;
        
        const offerLocationMain = offer.location.split(',')[0].trim().toLowerCase();
        if (request.location.some(loc => loc.toLowerCase().includes(offerLocationMain))) score += 0.2;

        const areaMatches = offer.area >= (request.areaRange?.min || 0) && offer.area <= (request.areaRange?.max || Infinity);
        if (areaMatches) score += 0.25;
        
        const priceMatches = offer.price >= (request.budgetRange?.min || 0) && offer.price <= (request.budgetRange?.max || Infinity);
        if (priceMatches) score += 0.25;

        if (score >= 0.7) { // Threshold for a match
          newMatches.push({
            id: `match-${offer.id}-${request.id}`,
            offer,
            request,
            matchScore: parseFloat(score.toFixed(2)),
            createdAt: new Date().toISOString(),
          });
        }
      });
    });
    setMatches(newMatches.sort((a,b) => b.matchScore - a.matchScore));
  }, [offers, requests]);

  useEffect(() => {
    runMatchingEngine();
  }, [offers, requests, runMatchingEngine]);


  const analyticsData = useMemo((): AnalyticsData => {
    const data: AnalyticsData = {
      totalOffers: offers.length,
      totalRequests: requests.length,
      offersByType: [],
      requestsByType: [],
      popularLocationsOffers: [],
      popularLocationsRequests: [],
      averagePriceByArea: [], // Simplified
      matchesFound: matches.length,
    };

    const offerTypeCounts: Record<string, number> = {};
    offers.forEach(o => {
      offerTypeCounts[o.propertyType] = (offerTypeCounts[o.propertyType] || 0) + 1;
      const mainLocation = o.location.split(',')[0].trim();
      const locIdx = data.popularLocationsOffers.findIndex(pl => pl.location === mainLocation);
      if(locIdx > -1) data.popularLocationsOffers[locIdx].count++;
      else data.popularLocationsOffers.push({location: mainLocation, count: 1});

      // Simplified price/area points
      data.averagePriceByArea.push({ area: o.area, price: o.price });
    });
    data.offersByType = Object.entries(offerTypeCounts).map(([type, count]) => ({ type: type as PropertyType, count }));
    data.popularLocationsOffers.sort((a,b) => b.count - a.count).splice(5); // Top 5

    const requestTypeCounts: Record<string, number> = {};
     requests.forEach(r => {
      r.propertyType.forEach(pt => {
        requestTypeCounts[pt] = (requestTypeCounts[pt] || 0) + 1;
      });
      const mainLocation = r.location[0]?.split(',')[0].trim() || 'Unknown';
      const locIdx = data.popularLocationsRequests.findIndex(pl => pl.location === mainLocation);
      if(locIdx > -1) data.popularLocationsRequests[locIdx].count++;
      else data.popularLocationsRequests.push({location: mainLocation, count: 1});
    });
    data.requestsByType = Object.entries(requestTypeCounts).map(([type, count]) => ({ type: type as PropertyType, count }));
    data.popularLocationsRequests.sort((a,b) => b.count - a.count).splice(5); // Top 5
    
    // Sort averagePriceByArea for chart
    data.averagePriceByArea.sort((a,b) => a.area - b.area);

    return data;
  }, [offers, requests, matches]);

  const PIE_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  // WhatsApp simulation handlers
  const handleConnectWhatsApp = () => {
    setWhatsappConnectionStatus(WHATSAPP_STATUS.CONNECTING);
    setShowQrPlaceholder(true);
    setTimeout(() => {
      setWhatsappConnectionStatus(WHATSAPP_STATUS.QR_REQUIRED);
    }, 1500);
    // In real app, this would initiate Playwright/Selenium connection
    // For now, simulate needing QR scan
    setTimeout(() => {
        if(whatsappConnectionStatus === WHATSAPP_STATUS.QR_REQUIRED) { // Check if user "scanned"
            // If not scanned, it might time out or stay in QR state
        }
    }, 15000); // QR timeout
  };
  
  const handleSimulateQrScan = () => {
      setWhatsappConnectionStatus(WHATSAPP_STATUS.CONNECTED);
      setShowQrPlaceholder(false);
  };

  const handleStartInitialScan = () => {
    if (whatsappConnectionStatus !== WHATSAPP_STATUS.CONNECTED) {
      alert("Please connect to WhatsApp first.");
      return;
    }
    setIsScanning(true);
    setScanProgress(0);
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setScanProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);
        setIsScanning(false);
        alert("Initial scan completed (simulation). New mock data might be added.");
        // Optionally add more mock data here
      }
    }, 300);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setChatFile(event.target.files[0]);
      // Simulate processing
      alert(`File "${event.target.files[0].name}" uploaded. Processing (simulation)... this would parse the .txt file and add leads.`);
      setChatFile(null); // Reset after "processing"
    }
  };

  const DashboardView = () => (
    <div className="p-6">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Dashboard</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-medium text-primary-600">Total Offers</h3>
          <p className="text-3xl font-bold text-gray-800">{analyticsData.totalOffers}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-medium text-secondary-600">Total Requests</h3>
          <p className="text-3xl font-bold text-gray-800">{analyticsData.totalRequests}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-medium text-green-600">Matches Found</h3>
          <p className="text-3xl font-bold text-gray-800">{analyticsData.matchesFound}</p>
        </div>
         <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-medium text-yellow-600">WhatsApp Status</h3>
          <p className={`text-xl font-bold ${whatsappConnectionStatus === WHATSAPP_STATUS.CONNECTED ? 'text-green-500' : 'text-red-500'}`}>{whatsappConnectionStatus}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Offers by Type</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie data={analyticsData.offersByType} dataKey="count" nameKey="type" cx="50%" cy="50%" outerRadius={100} fill="#8884d8" label>
                 {analyticsData.offersByType.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Requests by Type</h3>
           <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.requestsByType}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis allowDecimals={false}/>
              <Tooltip />
              <Legend />
              <Bar dataKey="count" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  const OffersView = () => (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Property Offers ({filteredOffers.length})</h2>
        <button
          onClick={() => { setEditingOffer(undefined); setIsOfferModalOpen(true); }}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
        >
          <PlusCircleIcon className="w-5 h-5 mr-2" /> Add Offer
        </button>
      </div>
      <FilterPanel filters={offerFilters} onFilterChange={handleOfferFilterChange} onResetFilters={resetOfferFilters} isOfferPanel={true} />
      {filteredOffers.length === 0 ? (
        <p className="text-center text-gray-500 py-8">No offers match the current filters, or no offers have been added yet.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredOffers.map(offer => <OfferCard key={offer.id} offer={offer} onEdit={openEditOfferModal} onDelete={handleDeleteOffer} />)}
        </div>
      )}
    </div>
  );

  const RequestsView = () => (
     <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Property Requests ({filteredRequests.length})</h2>
        <button
          onClick={() => { setEditingRequest(undefined); setIsRequestModalOpen(true); }}
          className="flex items-center px-4 py-2 bg-secondary-600 text-white rounded-md hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2 transition-colors"
        >
          <PlusCircleIcon className="w-5 h-5 mr-2" /> Add Request
        </button>
      </div>
      <FilterPanel filters={requestFilters} onFilterChange={handleRequestFilterChange} onResetFilters={resetRequestFilters} isOfferPanel={false} />
       {filteredRequests.length === 0 ? (
        <p className="text-center text-gray-500 py-8">No requests match the current filters, or no requests have been added yet.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredRequests.map(request => <RequestCard key={request.id} request={request} onEdit={openEditRequestModal} onDelete={handleDeleteRequest} />)}
        </div>
      )}
    </div>
  );

  const MatchesView = () => (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Matched Leads ({matches.length})</h2>
        <button
          onClick={runMatchingEngine}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
        >
          <ArrowPathIcon className="w-5 h-5 mr-2" /> Refresh Matches
        </button>
      </div>
      {matches.length === 0 ? (
         <p className="text-center text-gray-500 py-8">No matches found based on current offers and requests.</p>
      ) : (
        <div className="space-y-6">
          {matches.map(match => (
            <div key={match.id} className="bg-white shadow-lg rounded-lg overflow-hidden">
              <div className="p-4 bg-green-50 border-b border-green-200">
                <h3 className="text-lg font-semibold text-green-700">Match Found! (Score: {match.matchScore * 100}%)</h3>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="p-1"><OfferCard offer={match.offer} onEdit={openEditOfferModal} onDelete={handleDeleteOffer} /></div>
                <div className="p-1 border-t lg:border-t-0 lg:border-l border-gray-200"><RequestCard request={match.request} onEdit={openEditRequestModal} onDelete={handleDeleteRequest} /></div>
              </div>
               <div className="p-4 bg-gray-50 border-t border-gray-200">
                <p className="text-sm text-gray-700">
                  <strong>Offer Sender:</strong> {match.offer.sender.name || 'N/A'} ({match.offer.sender.id})
                  {match.offer.sender.groupName && ` from group "${match.offer.sender.groupName}"`}
                </p>
                <p className="text-sm text-gray-700 mt-1">
                  <strong>Request Sender:</strong> {match.request.sender.name || 'N/A'} ({match.request.sender.id})
                  {match.request.sender.groupName && ` from group "${match.request.sender.groupName}"`}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const AnalyticsView = () => (
    <div className="p-6">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Analytics & Reports</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
         <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Offers by Property Type</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie data={analyticsData.offersByType} dataKey="count" nameKey="type" cx="50%" cy="50%" outerRadius={100} fill="#8884d8" label>
                 {analyticsData.offersByType.map((entry, index) => (
                    <Cell key={`cell-offer-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
              </Pie>
              <Tooltip /> <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Requests by Property Type</h3>
           <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.requestsByType}>
              <CartesianGrid strokeDasharray="3 3" /> <XAxis dataKey="type" /> <YAxis allowDecimals={false}/> <Tooltip /> <Legend />
              <Bar dataKey="count" name="Requests">
                 {analyticsData.requestsByType.map((entry, index) => (
                    <Cell key={`cell-req-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
         <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Top 5 Offer Locations</h3>
           <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.popularLocationsOffers} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" /> <XAxis type="number" allowDecimals={false}/> <YAxis type="category" dataKey="location" width={100}/> <Tooltip /> <Legend />
              <Bar dataKey="count" name="Offers" fill="#FF8042" />
            </BarChart>
          </ResponsiveContainer>
        </div>
         <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Top 5 Request Locations</h3>
           <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.popularLocationsRequests} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" /> <XAxis type="number" allowDecimals={false}/> <YAxis type="category" dataKey="location" width={100}/> <Tooltip /> <Legend />
              <Bar dataKey="count" name="Requests" fill="#00C49F" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
  
  const SettingsView = () => (
    <div className="p-6">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Settings & WhatsApp Integration</h2>
      
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h3 className="text-xl font-semibold text-gray-700 mb-4">Gemini API Key</h3>
        <p className="text-sm text-gray-600 mb-2">
            The Gemini API Key should be configured via the <code>API_KEY</code> environment variable. 
            This application will attempt to use it automatically.
            <br/>Status: {process.env.API_KEY ? 
                <span className="text-green-600 font-semibold">Loaded from environment</span> : 
                <span className="text-red-600 font-semibold">Not found in environment (AI features might not work)</span>
            }
        </p>
        <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-xs text-yellow-700 flex items-start">
                <InformationCircleIcon className="w-5 h-5 mr-2 flex-shrink-0" />
                <span>
                    <strong>Important:</strong> For security reasons, API keys should not be hardcoded or managed directly in the frontend UI in a production application.
                    They are typically handled on a secure backend or through environment variables during deployment.
                </span>
            </p>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-xl font-semibold text-gray-700 mb-1">WhatsApp Integration (Simulation)</h3>
        <p className="text-sm text-gray-600 mb-4">
          This section simulates connecting to WhatsApp Web for lead extraction.
          Actual automation requires a backend setup (e.g., Playwright/Selenium on a server).
        </p>
        <div className="mb-4">
          <p className="font-medium text-gray-700">Status: 
            <span className={`ml-2 font-bold ${
              whatsappConnectionStatus === WHATSAPP_STATUS.CONNECTED ? 'text-green-600' : 
              whatsappConnectionStatus === WHATSAPP_STATUS.QR_REQUIRED ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {whatsappConnectionStatus}
            </span>
          </p>
        </div>

        {whatsappConnectionStatus === WHATSAPP_STATUS.DISCONNECTED && (
          <button 
            onClick={handleConnectWhatsApp}
            className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
          >
            Connect to WhatsApp Web
          </button>
        )}

        {showQrPlaceholder && whatsappConnectionStatus === WHATSAPP_STATUS.QR_REQUIRED && (
          <div className="my-4 p-4 border border-dashed border-gray-300 rounded-md text-center bg-gray-50">
            <QrCodeIcon className="w-32 h-32 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600 mb-2">Scan this QR code with your WhatsApp mobile app.</p>
            <p className="text-xs text-gray-500 mb-3">(This is a placeholder image)</p>
            <button 
                onClick={handleSimulateQrScan}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
                Simulate Successful Scan
            </button>
          </div>
        )}
        
        {whatsappConnectionStatus === WHATSAPP_STATUS.CONNECTED && !isScanning && (
          <div className="space-y-3">
            <button 
              onClick={handleStartInitialScan}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Start Initial Historical Scan (Simulation)
            </button>
             <button 
              onClick={() => setWhatsappConnectionStatus(WHATSAPP_STATUS.DISCONNECTED)}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
            >
              Disconnect from WhatsApp
            </button>
          </div>
        )}

        {isScanning && (
          <div className="my-4">
            <p className="text-gray-700 mb-1">Scanning messages... {scanProgress}%</p>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${scanProgress}%` }}></div>
            </div>
          </div>
        )}

        <div className="mt-6 border-t pt-6">
            <h4 className="text-lg font-semibold text-gray-700 mb-2">Upload Chat Export (.txt)</h4>
            <p className="text-sm text-gray-600 mb-3">
                For large historical data, you can upload a WhatsApp chat export file.
            </p>
            <input 
                type="file" 
                accept=".txt" 
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
            />
        </div>
         <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-xs text-yellow-700 flex items-start">
                <InformationCircleIcon className="w-5 h-5 mr-2 flex-shrink-0" />
                <span>
                    <strong>Note for Operation:</strong> Actual WhatsApp automation requires the device running this bot (laptop/VPS) to be constantly on and connected to the internet. The mobile phone linked to the WhatsApp account must also remain connected to the internet.
                </span>
            </p>
        </div>
      </div>
    </div>
  );


  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className="w-64 bg-primary-800 text-primary-100 flex flex-col">
        <div className="h-16 flex items-center justify-center shadow-md">
          <Link to="/" className="text-xl font-semibold px-4">{APP_NAME}</Link>
        </div>
        <nav className="flex-grow p-4 space-y-2">
          {NAVIGATION_ITEMS.map(item => (
            <Link
              key={item.view}
              to={`/${item.view}`}
              className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors
                ${currentView === item.view ? 'bg-primary-900 text-white' : 'hover:bg-primary-700 hover:text-white'}`}
            >
              <item.icon className="w-5 h-5 mr-3" />
              {item.name}
            </Link>
          ))}
        </nav>
        <div className="p-4 border-t border-primary-700">
            <p className="text-xs text-primary-300">© {new Date().getFullYear()} RE Leads Pro</p>
        </div>
      </aside>

      {/* Main content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        <header className="h-16 bg-white shadow-md flex items-center justify-between px-6">
          <h1 className="text-xl font-semibold text-gray-700 capitalize">
            {currentView.replace('_', ' ')}
          </h1>
          <div className="flex items-center space-x-3">
             <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                whatsappConnectionStatus === WHATSAPP_STATUS.CONNECTED ? 'bg-green-100 text-green-700' :
                whatsappConnectionStatus === WHATSAPP_STATUS.QR_REQUIRED ? 'bg-yellow-100 text-yellow-700' :
                'bg-red-100 text-red-700'
             }`}>
                WhatsApp: {whatsappConnectionStatus}
             </span>
            <UserCircleIcon className="w-8 h-8 text-gray-500" />
          </div>
        </header>
        <div className="flex-1 overflow-y-auto p-0"> {/* Adjusted padding for page-specific padding */}
          <Routes>
            <Route path="/" element={<Navigate to={`/${AppView.DASHBOARD}`} replace />} />
            <Route path={`/${AppView.DASHBOARD}`} element={<DashboardView />} />
            <Route path={`/${AppView.OFFERS}`} element={<OffersView />} />
            <Route path={`/${AppView.REQUESTS}`} element={<RequestsView />} />
            <Route path={`/${AppView.MATCHES}`} element={<MatchesView />} />
            <Route path={`/${AppView.ANALYTICS}`} element={<AnalyticsView />} />
            <Route path={`/${AppView.SETTINGS}`} element={<SettingsView />} />
            {/* Add more routes as needed */}
          </Routes>
        </div>
      </main>

      {/* Modals */}
      <Modal isOpen={isOfferModalOpen} onClose={() => { setIsOfferModalOpen(false); setEditingOffer(undefined); }} title={editingOffer ? "Edit Offer" : "Add New Offer"} size="3xl">
        <OfferForm 
          onSubmit={editingOffer ? handleEditOffer : handleAddOffer} 
          onCancel={() => { setIsOfferModalOpen(false); setEditingOffer(undefined); }}
          initialData={editingOffer}
        />
      </Modal>
      <Modal isOpen={isRequestModalOpen} onClose={() => { setIsRequestModalOpen(false); setEditingRequest(undefined); }} title={editingRequest ? "Edit Request" : "Add New Request"} size="3xl">
        <RequestForm 
          onSubmit={editingRequest ? handleEditRequest : handleAddRequest}
          onCancel={() => { setIsRequestModalOpen(false); setEditingRequest(undefined); }}
          initialData={editingRequest}
        />
      </Modal>
    </div>
  );
};

export default App;
