
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { PropertyOffer, PropertyRequest, PropertyType, DirectnessType, DirectnessInfo } from '../types';
import { GEMINI_MODEL_TEXT } from '../constants';

// Ensure API_KEY is available. In a real app, this would be set in the environment.
// For this example, we'll use a placeholder or expect it to be set.
const apiKey = process.env.API_KEY;

if (!apiKey) {
  console.warn(
    "API_KEY environment variable is not set. Gemini API calls will fail. " +
    "Please set it in your .env file or environment configuration. " +
    "For development, you can temporarily set it here but REMOVE before committing."
  );
  // process.env.API_KEY = "YOUR_API_KEY_HERE_FOR_LOCAL_DEV_ONLY"; // DON'T COMMIT THIS LINE
}


const ai = new GoogleGenAI({ apiKey: apiKey || "MISSING_API_KEY" });

const parseJsonFromMarkdown = <T,>(text: string): T | null => {
  let jsonStr = text.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }
  try {
    return JSON.parse(jsonStr) as T;
  } catch (e) {
    console.error("Failed to parse JSON response:", e, "Original text:", text);
    // Try to find JSON within the string if it's not perfectly formatted
    const jsonStart = jsonStr.indexOf('{');
    const jsonEnd = jsonStr.lastIndexOf('}');
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        return JSON.parse(jsonStr.substring(jsonStart, jsonEnd + 1)) as T;
      } catch (e2) {
        console.error("Failed to parse substring JSON response:", e2);
      }
    }
    return null;
  }
};


export const extractPropertyDetailsFromText = async (
  text: string,
  type: 'offer' | 'request'
): Promise<Partial<PropertyOffer> | Partial<PropertyRequest> | null> => {
  if (!apiKey || apiKey === "MISSING_API_KEY") {
    console.error("Gemini API key is not configured.");
    // Return a mock error or a predefined structure to indicate failure
    // This helps in UI testing without a live API key
    return { 
        description: `(Simulated AI Extraction - API Key Missing) Based on text: "${text.substring(0,50)}..."` 
    };
  }

  const propertyTypesString = Object.values(PropertyType).join(', ');
  const directnessTypesString = Object.values(DirectnessType).join(', ');

  const commonSchema = `
    "location": "string (e.g., City, Neighborhood, Street)",
    "area": "number (in square meters, e.g., 150, only for offers; for requests use areaRangeMin/Max)",
    "price": "number (e.g., 500000, if it's a range for request, use budgetRangeMin/Max)",
    "currency": "string (e.g., SAR, USD)",
    "rooms": "number (optional, e.g., 3)",
    "bathrooms": "number (optional, e.g., 2)",
    "finishType": "string (optional, e.g., Super Lux, Semi-finished)",
    "isFurnished": "boolean (optional)",
    "features": "array of strings (optional, e.g., ['Pool', 'Garden'])",
    "description": "string (a brief summary or any additional notes extracted)",
    "senderName": "string (sender's name if mentioned, otherwise null)",
    "senderContact": "string (sender's phone number or contact ID if mentioned, otherwise null)"
  `;

  const offerSchema = `
    "propertyType": "enum (One of: ${propertyTypesString})",
    ${commonSchema},
    "directnessType": "enum (One of: ${directnessTypesString})",
    "brokers": "number (optional, only if directnessType is '${DirectnessType.INDIRECT}', indicates number of brokers, e.g., 2)"
  `;

  const requestSchema = `
    "propertyType": "array of enums (One or more of: ${propertyTypesString})",
    "location": "array of strings (preferred locations)",
    "areaRangeMin": "number (optional, minimum area in sqm)",
    "areaRangeMax": "number (optional, maximum area in sqm)",
    "budgetRangeMin": "number (optional, minimum budget)",
    "budgetRangeMax": "number (optional, maximum budget)",
    ${commonSchema} 
  `;
 // Note for AI: "area" should not be in common schema for requests, "price" should not be in common schema for requests. Modified above.

  const prompt = `
    You are an expert real estate data extraction assistant. Analyze the following WhatsApp message content and extract structured information.
    The message content is:
    ---
    ${text}
    ---
    Identify if this message primarily describes a property ${type === 'offer' ? 'offer (for sale/rent)' : 'request (looking for property)'}.
    
    Extract the following details and return them in a VALID JSON format.
    ${type === 'offer' ? `For an OFFER, use this JSON schema: { ${offerSchema} }` : `For a REQUEST, use this JSON schema: { ${requestSchema} }`}

    Guidelines:
    - If a value is not found, use null or omit the key if appropriate for optional fields.
    - For 'location', be as specific as possible. If multiple locations are mentioned for a request, include them in the array.
    - For 'directnessType' (offers only), infer from phrases like "من المالك مباشرة" (Very Direct), "مباشر" (Direct), "معايا 2 وسطاء" (Indirect, brokers: 2).
    - If 'directnessType' is '${DirectnessType.INDIRECT}' and the number of brokers is mentioned (e.g., "معي ٢", "3 وسطاء"), extract that number into the 'brokers' field.
    - Currency should be identified (e.g., ريال, درهم, جنيه, دولار).
    - 'features' should be an array of distinct amenities or characteristics.
    - 'description' should capture any other relevant information not fitting other fields.
    - If a price or budget is a range (e.g., "2M to 2.2M"), extract min and max values. For offers, price is usually a single value.
    - 'propertyType' for offers should be a single value. For requests, it can be an array if multiple types are sought.
    - Accurately parse numbers for area (offers), price (offers), rooms, bathrooms. For requests, use areaRangeMin/Max and budgetRangeMin/Max.
    - If the message is not a valid real estate ${type}, return an empty JSON object {} or { "error": "Not a valid ${type}" }.
    
    Return ONLY the JSON object. Do not include any explanatory text before or after the JSON.
  `;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_MODEL_TEXT,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.2, // Lower temperature for more deterministic extraction
      },
    });

    const extractedText = response.text;
    if (!extractedText) {
      console.error("Gemini API returned no text.");
      return null;
    }
    
    const parsedData = parseJsonFromMarkdown<any>(extractedText);

    if (!parsedData || Object.keys(parsedData).length === 0 || parsedData.error) {
        console.warn("Gemini could not extract valid data or identified as not a valid type:", parsedData?.error || "Empty data");
        return { description: `AI could not confidently extract details. Original: ${text.substring(0,100)}...` };
    }
    
    // Transform parsedData to fit PropertyOffer/PropertyRequest structure
    const transformedData: Partial<PropertyOffer> | Partial<PropertyRequest> = {};

    // Common properties assignable to the base of transformedData
    if (parsedData.location) transformedData.location = parsedData.location; // Will be refined in type-specific blocks if needed (string vs string[])
    if (parsedData.rooms) transformedData.rooms = Number(parsedData.rooms);
    if (parsedData.bathrooms) transformedData.bathrooms = Number(parsedData.bathrooms);
    if (parsedData.finishType) transformedData.finishType = parsedData.finishType;
    if (typeof parsedData.isFurnished === 'boolean') transformedData.isFurnished = parsedData.isFurnished;
    if (parsedData.features) transformedData.features = parsedData.features;
    if (parsedData.description) transformedData.description = parsedData.description;
    
    const sender: Partial<PropertyOffer['sender'] | PropertyRequest['sender']> = {};
    if (parsedData.senderName) sender.name = parsedData.senderName;
    if (parsedData.senderContact) sender.id = parsedData.senderContact; // Assuming contact is ID
    if (Object.keys(sender).length > 0) {
        // @ts-ignore sender is compatible
        transformedData.sender = sender;
    }


    if (type === 'offer') {
      const offerData = transformedData as Partial<PropertyOffer>;
      if (typeof parsedData.location === 'string') offerData.location = parsedData.location; // Ensure location is string for offer

      if (parsedData.propertyType && Object.values(PropertyType).includes(parsedData.propertyType as PropertyType)) {
        offerData.propertyType = parsedData.propertyType as PropertyType;
      }
      if (parsedData.area) offerData.area = Number(parsedData.area); // Area specific to Offer
      if (parsedData.price) offerData.price = Number(parsedData.price); // Price specific to Offer
      if (parsedData.currency) offerData.currency = parsedData.currency; // Currency for Offer

      if (parsedData.directnessType && Object.values(DirectnessType).includes(parsedData.directnessType as DirectnessType)) {
        const directness: DirectnessInfo = { type: parsedData.directnessType as DirectnessType };
        if (parsedData.directnessType === DirectnessType.INDIRECT && parsedData.brokers) {
          directness.brokers = Number(parsedData.brokers);
        }
        offerData.directness = directness;
      }
      return offerData;
    } else { // request
      const requestData = transformedData as Partial<PropertyRequest>;
      // Ensure location is string[] for request
      if (parsedData.location) {
        requestData.location = Array.isArray(parsedData.location) ? parsedData.location : [String(parsedData.location)];
      }


      if (parsedData.propertyType && Array.isArray(parsedData.propertyType)) {
        requestData.propertyType = parsedData.propertyType.filter((pt: string) => Object.values(PropertyType).includes(pt as PropertyType)) as PropertyType[];
      } else if (parsedData.propertyType && Object.values(PropertyType).includes(parsedData.propertyType as PropertyType)) {
        requestData.propertyType = [parsedData.propertyType as PropertyType];
      }
      if (parsedData.currency) requestData.currency = parsedData.currency; // Currency for Request

      if (parsedData.areaRangeMin || parsedData.areaRangeMax) {
        requestData.areaRange = { 
          min: Number(parsedData.areaRangeMin) || 0, 
          max: Number(parsedData.areaRangeMax) || Number.POSITIVE_INFINITY 
        };
      }
      if (parsedData.budgetRangeMin || parsedData.budgetRangeMax) {
        requestData.budgetRange = { 
          min: Number(parsedData.budgetRangeMin) || 0, 
          max: Number(parsedData.budgetRangeMax) || Number.POSITIVE_INFINITY
        };
      }
       // If price was extracted (less common for requests but possible by AI)
      if (parsedData.price && !requestData.budgetRange) { // If AI returns 'price' for a request, interpret as budget
        requestData.budgetRange = { min: Number(parsedData.price) * 0.9, max: Number(parsedData.price) * 1.1 }; // Create a small range
      }
      return requestData;
    }

  } catch (error) {
    console.error("Error calling Gemini API or processing response:", error);
    // Return a partial object with an error message in description for UI display
    return { 
        description: `(AI Extraction Error) Failed to process text. Original: "${text.substring(0,50)}..."` 
    };
  }
};