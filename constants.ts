
import { PropertyType, DirectnessType, AppView } from './types';
import { HomeIcon, BriefcaseIcon, MapPinIcon, UsersIcon, ChartBarIcon, Cog6ToothIcon, PlusCircleIcon, DocumentTextIcon, MagnifyingGlassIcon, LightBulbIcon } from './components/Icons'; // Assuming Icons.tsx exists
import React from 'react';

export const APP_NAME = "Smart Real Estate Leads Manager";

export const PROPERTY_TYPE_OPTIONS = Object.values(PropertyType);
export const DIRECTNESS_TYPE_OPTIONS = Object.values(DirectnessType);
export const CURRENCY_OPTIONS = ["USD", "EUR", "SAR", "AED", "EGP"]; // Add more as needed

export const DEFAULT_CURRENCY = "SAR";

export const NAVIGATION_ITEMS: { name: string; view: AppView; icon: React.FC<React.SVGProps<SVGSVGElement>> }[] = [
  { name: "Dashboard", view: AppView.DASHBOARD, icon: HomeIcon },
  { name: "Offers", view: AppView.OFFERS, icon: BriefcaseIcon },
  { name: "Requests", view: AppView.REQUESTS, icon: DocumentTextIcon },
  { name: "Matches", view: AppView.MATCHES, icon: MagnifyingGlassIcon },
  { name: "Analytics", view: AppView.ANALYTICS, icon: ChartBarIcon },
  { name: "Settings", view: AppView.SETTINGS, icon: Cog6ToothIcon },
];

export const MOCK_API_KEY_PLACEHOLDER = "YOUR_GEMINI_API_KEY"; // Placeholder for API key in UI settings if needed, actual key from process.env

export const GEMINI_MODEL_TEXT = 'gemini-2.5-flash-preview-04-17';
// export const GEMINI_MODEL_IMAGE = 'imagen-3.0-generate-002'; // If image generation were needed

export const INITIAL_OFFERS_COUNT = 5;
export const INITIAL_REQUESTS_COUNT = 3;

export const WHATSAPP_STATUS = {
  DISCONNECTED: "Disconnected",
  CONNECTING: "Connecting...",
  CONNECTED: "Connected",
  QR_REQUIRED: "QR Code Scan Required",
  ERROR: "Error"
};

export const DIRECTNESS_COLORS: Record<DirectnessType, string> = {
  [DirectnessType.VERY_DIRECT]: "bg-green-100 text-green-700",
  [DirectnessType.DIRECT]: "bg-blue-100 text-blue-700",
  [DirectnessType.INDIRECT]: "bg-yellow-100 text-yellow-700",
};

export const MIN_BROKERS = 1;
export const MAX_BROKERS = 10;
